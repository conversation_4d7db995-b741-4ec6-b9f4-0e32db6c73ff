nav {
    width: 100%;
}

.header {
    width: 1230px;
    margin: 0 auto;
}

.header > #mallLogo {
    float: left;
    padding-top: 28px;
    width: 280px;
    height: 64px;
    line-height: 64px;
    position: relative;
}

#mallLogo > a {
    position: relative;
    display: block;
    width: 190px;
    height: 30px;
    overflow: hidden;
}

#mallLogo img {
    width: 190px;
    height: 28px;
    cursor: pointer;
    vertical-align: top;
}

.header > .shopSearchHeader {
    overflow: hidden;
    width: 597px;
    padding-top: 25px;
}

.shopSearchHeader > form {
    border: solid #ff0036;
    border-width: 3px 0 3px 3px;
}

.shopSearchHeader > form > .shopSearchInput {
    position: relative;
    height: 30px;
}

input::-webkit-input-placeholder { /* WebKit browsers*/
    color: #ccc;
    font-weight: normal;
}

input:-moz-placeholder { /* Mozilla Firefox 4 to 18*/
    color: #ccc;
    font-weight: normal;
}

input::-moz-placeholder { /* Mozilla Firefox 19+*/
    color: #ccc;
    font-weight: normal;
}

input:-ms-input-placeholder { /* Internet Explorer 10+*/
    color: #ccc;
    font-weight: normal;
}

.shopSearchInput > .searchInput {
    font-size: 12px;
    color: #000;
    width: 496px;
    height: 20px;
    line-height: 20px;
    padding: 5px 3px 5px 5px;
    border: none;
    font-weight: 900;
    outline: none;
    float: left;
}

.shopSearchInput > .searchBtn {
    width: 90px;
    height: 30px;
    font-size: 16px;
    cursor: pointer;
    color: #ffffff;
    background-color: #FF0036;
    overflow: hidden;
    border: 0;
    font-family: Arial, serif;
    float: left;
}

.shopSearchHeader > ul {
    padding-top: 4px;
    margin-left: -10px;
    height: 16px;
    overflow: hidden;
    line-height: 16px;
    margin-bottom: 15px;
}

.shopSearchHeader li + li {
    border-left: 1px solid #cccccc;
}

.shopSearchHeader li {
    float: left;
    line-height: 1.1;
    padding: 0 12px;
}

.shopSearchHeader li > a {
    color: #999;
    font-size: 12px;
}

.context {
    width: 100%;
}

.context > .context_menu {
    width: 1230px;
    margin: 0 auto;
    background-color: #faf9f9;
    padding: 5px;
    height: 23px;
}

.context_menu > ul {
    display: inline-block;
    border: 1px solid #cccccc;
    border-right: none;
    margin-bottom: 0;
    margin-right: 5px;
}

.context_menu > ul > li {
    float: left;
    border-right: 1px solid #cccccc;
}

.context_menu > ul > li {
    color: #806f66;
    text-align: center;
    display: block;
    width: 50px;
    padding: 2px;
    font-size: 12px;
    cursor: pointer;
}

.context_menu li:hover {
    text-decoration: none;
    color: #ff0036;
    background-color: #F1EDEC;
}

.context_menu li.orderBySelect {
    background: #F1EDEC;
}

.context_menu li.orderBySelect > span {
    color: #ff0036;
}

.context_menu li.orderBySelect > span.orderByDesc.orderBySelect {
    border-bottom-color: #ff0036;
}

.context_menu li.orderBySelect > span.orderByAsc.orderBySelect {
    border-top-color: #ff0036;
}

.context_menu li > span.orderByDesc {
    display: inline-block;
    position: relative;
    bottom: 6px;
    width: 0;
    height: 0;
    border: 4px solid transparent;
    border-bottom-color: #806f66;
}

.context_menu li > span.orderByAsc {
    display: inline-block;
    position: relative;
    width: 0;
    height: 0;
    top: 2px;
    border: 4px solid transparent;
    border-top-color: #806f66;
}

.context_menu_liHover {
    color: #ff0036;
    background-color: #F1EDEC;
}

.context > .context_main {
    width: 1230px;
    margin: 0 auto;
    padding: 10px 5px;
}

.context_main > .context_productStyle {
    width: 220px;
    height: 372px;
    position: relative;
    display: inline-block;
    margin-right: 20px;
    margin-bottom: 25px;
}

.context_productStyle > .context_product {
    width: 210px;
    background-color: #ffffff;
    padding: 4px 4px 0;
    border: 1px solid #f5f5f5;
    border-radius: 3px;
}

.context_product:hover {
    height: auto;
    margin: -3px;
    border: 4px solid #ff0036;
    border-radius: 0;
    -webkit-transition: border-color .2s ease-in;
    -moz-transition: border-color .2s ease-in;
    -o-transition: border-color .2s ease-in;;
    transition: border-color .2s ease-in;
}

.context_product .context_product_imgMain {
    width: 210px;
    height: 210px;
    margin-bottom: 5px;
}

.context_product > .context_product_imgList {
    text-align: center;
    margin-bottom: 5px;
    padding-top: 5px;
    width: 100%;
    min-height: 37px;
}

.context_product_imgList > li {
    cursor: pointer;
    display: inline-block;
    border: 1px solid #cccccc;
    padding: 1px;
}

.context_product_imgList > .context_product_Li_click {
    border: 2px solid #cd0000;
    padding: 0;
}

.context_product_imgList img {
    width: 30px;
    height: 30px;
}

.context_product > .context_product_price {
    font-family: arial, serif;
    color: #ff0036;
    font-size: 20px;
    height: 30px;
    display: block;
    line-height: 30px;
    margin: 0 0 5px;
    overflow: hidden;
    font-weight: 400;
}

.context_product_price > span {
    font-family: arial, serif;
    margin-right: 2px;
    font-weight: 700;
    font-size: 14px;
}

.context_product > .context_product_name {
    display: block;
    height: 16px;
    margin: 0 0 3px;
    word-break: break-all;
    position: relative;
    overflow: hidden;
}

.context_product_name > a {
    font-family: \5FAE\8F6F\96C5\9ED1, serif;
    color: #333333;
    font-size: 12px;
}

.context_product_name > a:hover {
    color: #ff0036;
    text-decoration: underline;
}

.context_product > .context_product_shop {
    position: relative;
    height: 22px;
    line-height: 20px;
    margin: 0 0 5px;
    white-space: nowrap;
    overflow: hidden;
}

.context_product_shop > span {
    color: #999;
    text-decoration: underline;
    text-overflow: ellipsis;
    font-size: 12px;
    white-space: nowrap;
}

.context_product > .context_product_status {
    position: relative;
    height: 32px;
    border: none;
    border-top: 1px solid #eee;
    margin: 0;
    color: #999;
}

.context_product_status > .status_left {
    float: left;
    border-right: 1px solid #eeeeee;
    width: 39%;
    padding: 10px 1px;
    margin-right: 6px;
    line-height: 12px;
    text-align: left;
    font-size: 12px;
    color: #999;
}

.status_left > em {
    position: absolute;
    color: #b57c5b;
    padding-left: 2px;
    font-family: Arial, serif;
    font-size: 12px;
    font-weight: 700;
    font-style: normal;
}

.context_product_status > .status_middle {
    float: left;
    border-right: 1px solid #eeeeee;
    width: 39%;
    padding: 10px 1px;
    margin-right: 6px;
    line-height: 12px;
    text-align: left;
    font-size: 12px;
    color: #999;
}

.status_middle > em {
    color: #38b;
    padding-left: 2px;
    font-family: Arial, serif;
    font-size: 12px;
    font-weight: 700;
    font-style: normal;
}

.context_product_status > .status_right {
    float: left;
    width: 9%;
    margin-right: 6px;
    padding: 6px 1px;
}

.status_right > img {
    position: relative;
    bottom: 2px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    margin: 0;
    padding: 0;
}

.error {
    width: 1095px;
    padding: 8px 20px 20px 115px;
    background: url(../../images/fore/WebsiteImage/TB1QJqdPFXXXXccXFXXXXXXXXXX-65-48.png) 23px center no-repeat #fff8f6;
    border: 1px solid #f7eae7;
    color: #595959;
    margin: 0 auto 10px;
}

.error > h2 {
    font-size: 14px;
    font-weight: 700;
    color: #333;
    line-height: 38px;
}

.error > h3 {
    font-size: 12px;
    line-height: 36px;
    font-weight: bold;
}

.error > ol {
    list-style: none;
}

.error > ol > li {
    font-size: 12px;
    margin-bottom: 5px;
    list-style: decimal inside;
}

.error form {
    display: inline-block;
}

.error .errorInput {
    width: 180px;
    padding: 4px 5px;
    height: 15px;
    line-height: 15px;
    margin: 2px 10px 0 0;
    border: 1px solid #e5e5e5;
    display: inline-block;
}

.error .errorBtn {
    background-color: #ff0036;
    color: #fff;
    height: 25px;
    line-height: 25px;
    font-size: 12px;
    font-weight: 700;
    padding: 0 10px;
    display: inline-block;
    border: 0;
    border-left: 1px solid #ff0036;
    border-right: 1px solid #ff0036;
    border-radius: 2px;
}