package com.xq.tmall.controller.fore;

import com.xq.tmall.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 * 前台页面跳转控制器
 * 专门处理JSP页面跳转，不涉及数据处理
 */
@Api(tags = "前台页面跳转")
@Controller
public class ForePageController extends BaseController {

    // 转到前台天猫-主页
    @ApiOperation(value = "转到前台天猫-主页", notes = "转到前台天猫-主页")
    @GetMapping(value = "/")
    public String goToHomePage() {
        return "fore/homePage";
    }

    // 转到前台天猫-错误页
    @ApiOperation(value = "转到前台天猫-错误页", notes = "转到前台天猫-错误页")
    @GetMapping(value = "error")
    public String goToErrorPage() {
        return "fore/errorPage";
    }

    // 转到前台天猫-登录页
    @ApiOperation(value = "转到前台天猫-登录页", notes = "转到前台天猫-登录页")
    @GetMapping(value = "login")
    public String goToLoginPage() {
        return "fore/loginPage";
    }
}
