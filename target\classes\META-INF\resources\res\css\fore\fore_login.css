.content {
    position: relative;
    width: 100%;
    min-width: 1190px;
    margin-bottom: 40px;
    background-color: rgb(241, 220, 165);
    font-family: "Microsoft YaHei UI", serif;
}

.content > .contentMain {
    position: relative;
    top: 0;
    left: 0;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    width: 100%;
    height: 600px;
    background-image: url(../../images/fore/WebsiteImage/login.png);
}

.loginDiv {
    width: 350px;
    height: 400px;
    position: absolute;
    top: 90px;
    right: 350px;
    background: #ffffff;
    color: #404040;
}

.loginSwitch {
    position: absolute;
    width: 52px;
    height: 52px;
    right: 5px;
    top: 12px;
    background-image: url(../../images/fore/WebsiteImage/scan_icon.png);
    cursor: pointer;
    -webkit-background-size: cover;
    background-size: cover;
}

.loginSwitch_two {
    position: absolute;
    width: 52px;
    height: 52px;
    right: 5px;
    top: 12px;
    background-image: url(../../images/fore/WebsiteImage/pc_icon.png);
    cursor: pointer;
    -webkit-background-size: cover;
    background-size: cover;
}

.loginDiv > .loginMessage {
    position: absolute;
    top: 10px;
    right: 58px;
}

.loginMessage > .loginMessageMain {
    border: 1px solid #f3d995;
    height: 16px;
    line-height: 16px;
    background: #fefcee;
    color: #df9c1f;
    font-size: 12px;
    font-weight: 400;
    padding: 5px 20px 5px 15px;
    position: relative;
}

.loginMessageMain > img {
    margin-right: 8px;
    position: relative;
    bottom: 2px;
}

.loginMessageMain > .poptip-arrow {
    position: absolute;
    top: 8px;
    right: 0;
}

.poptip-arrow > em {
    position: absolute;
    width: 0;
    height: 0;
    top: 0;
    left: 1px;
    border-width: 6px 0 6px 6px;
    border-style: solid;
    border-color: rgba(255, 255, 255, 0);
    border-left-color: #f3d995;
}

.poptip-arrow > span {
    border-width: 6px 0 6px 6px;
    position: absolute;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
    border-style: solid;
    border-color: rgba(255, 255, 255, 0);
    border-left-color: #fefcee;
}

.loginDiv > .pwdLogin {
    padding: 25px 26px 20px;
}

.pwdLogin .loginInputDiv {
    position: relative;
    margin-top: 25px;

}

.loginInputDiv > .loginLabel {
    display: block;
    width: 38px;
    height: 38px;
    line-height: 38px;
    background: #ddd;
    text-align: center;
    position: absolute;
    top: 1px;
    left: 1px;
    font-size: 18px;
}

.loginInputDiv > .loginCode {
    width: 140px;
    height: 38px;
    text-align: center;
}

.loginLabel > i {
    color: #606060;
}

.loginInputDiv > .loginInput {
    color: #9b9b9b;
    width: 240px;
    font-size: 14px;
    line-height: 18px;
    height: 18px;
    padding: 11px 8px 11px 50px;
    border: 1px solid #CBCBCB;
}

.pwdLogin .loginButton {
    width: 300px;
    height: 42px;
    line-height: 42px;
    background-color: #ff0036;
    font-weight: 700;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    border-radius: 3px;
    border: 0;
    margin-top: 20px;
}

.loginButton:hover {
    background-color: #ff335e;
}

.pwdLogin .loginLinks {
    margin-top: 15px;
    text-align: right;
    font-size: 12px;
}

.loginLinks > a {
    margin-right: 10px;
    color: #6c6c6c;
    text-decoration: none;
}

.loginLinks > a:hover {
    color: #FF0036;
}

.pwdLogin .error_message {
    display: block;
    border-color: #ffb4a8;
    background-color: #fef2f2;
    margin-top: 15px;
    position: relative;
}

.error_message > p {
    position: relative;
    color: #c33;
    left: 20px;
    opacity: 0;
    margin: 0;
}

.loginTitle {
    display: block;
    margin-top: 15px;
    height: 18px;
    line-height: 18px;
    font-size: 16px;
    color: #3c3c3c;
    font-weight: 700;
}

.loginDiv > .qrcodeLogin {
    display: none;
    padding: 25px 26px 20px;
}

.qrcodeLogin > .qrcodeMain {
    position: relative;
    margin-top: 24px;
    height: 140px;
}

.qrcodeMain > #qrcodeA {
    position: absolute;
    left: 80px;
}

.qrcodeMain > #qrcodeB {
    position: absolute;
    opacity: 0;
    right: 12px;
    top: -15px;
}

.qrcodeLogin > .qrcodeFooter {
    width: 188px;
    margin: 24px auto 0;
    overflow: hidden;
}

.qrcodeFooter > img {
    float: left;
    margin-right: 10px;
    padding-top: 5px;
}

.qrcodeFooter > p {
    float: left;
    width: 144px;
    line-height: 18px;
    color: #6c6c6c;
    font-size: 12px;
}

.qrcodeFooter > p > a {
    color: #ff0036;
}

.qrcodeFooter > p > a:hover {
    text-decoration: none;
}

.qrcodeLogin > .loginLinks {
    margin-top: 15px;
    overflow: hidden;
    text-align: right;
    font-size: 12px;
}