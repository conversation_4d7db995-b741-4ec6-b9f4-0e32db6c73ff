/**
 * 首页API数据加载和渲染模块
 */
var HomeAPI = {
    
    // API基础路径
    baseUrl: '',
    
    // 初始化
    init: function() {
        this.baseUrl = this.getContextPath();
        this.loadHomeData();
    },
    
    // 获取上下文路径
    getContextPath: function() {
        var pathName = document.location.pathname;
        var index = pathName.substr(1).indexOf("/");
        var result = pathName.substr(0, index + 1);
        return result === "/" ? "" : result;
    },
    
    // 获取JWT Token
    getToken: function() {
        return localStorage.getItem('jwt_token');
    },
    
    // 设置JWT Token
    setToken: function(token) {
        localStorage.setItem('jwt_token', token);
    },
    
    // 移除JWT Token
    removeToken: function() {
        localStorage.removeItem('jwt_token');
    },
    
    // 加载首页数据
    loadHomeData: function() {
        var self = this;
        var token = this.getToken();
        
        $.ajax({
            url: self.baseUrl + '/api/home',
            type: 'GET',
            headers: token ? {'Authorization': 'Bearer ' + token} : {},
            dataType: 'json',
            success: function(response) {
                if (response.code === 200) {
                    self.renderHomeData(response.data);
                } else {
                    console.error('获取首页数据失败：', response.message);
                    self.showError('获取首页数据失败：' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('请求失败：', error);
                self.showError('网络请求失败，请稍后重试');
            }
        });
    },
    
    // 渲染首页数据
    renderHomeData: function(data) {
        // 渲染用户信息
        this.renderUserInfo(data.user);
        
        // 渲染分类列表
        this.renderCategoryList(data.categoryList);
        
        // 渲染促销产品
        this.renderSpecialProducts(data.specialProductList);
        
        // 渲染分类下的产品
        this.renderCategoryProducts(data.categoryList);
    },
    
    // 渲染用户信息
    renderUserInfo: function(user) {
        var loginContainer = $('#container_login');
        if (user) {
            // 用户已登录
            loginContainer.html(
                '<em>Hi，</em>' +
                '<a href="' + this.baseUrl + '/userDetails" class="userName" target="_blank">' + user.user_name + '</a>' +
                '<a href="javascript:void(0)" onclick="HomeAPI.logout()">退出</a>'
            );
        } else {
            // 用户未登录
            loginContainer.html(
                '<em>喵，欢迎来天猫</em>' +
                '<a href="' + this.baseUrl + '/login">请登录</a>' +
                '<a href="' + this.baseUrl + '/register">免费注册</a>'
            );
        }
    },
    
    // 渲染分类列表
    renderCategoryList: function(categoryList) {
        if (!categoryList || categoryList.length === 0) return;
        
        // 渲染搜索区域的分类链接
        var searchCategoryHtml = '';
        for (var i = 0; i < categoryList.length && i < 9; i++) {
            var category = categoryList[i];
            var categoryName = category.category_name;
            if (categoryName.indexOf(' /') > -1) {
                categoryName = categoryName.substring(0, categoryName.indexOf(' /'));
            }
            var colorStyle = i % 2 !== 0 ? ' style="color: #FF0036"' : '';
            searchCategoryHtml += '<li><a href="' + this.baseUrl + '/product?category_id=' + 
                                 category.category_id + '"' + colorStyle + '>' + categoryName + '</a></li>';
        }
        $('.mallSearch ul').html(searchCategoryHtml);
        
        // 渲染导航区域的分类
        var navCategoryHtml = '';
        for (var j = 0; j < categoryList.length; j++) {
            var cat = categoryList[j];
            navCategoryHtml += '<li data-toggle="' + cat.category_id + '" data-status="">' +
                              '<img src="' + this.baseUrl + '/res/images/fore/WebsiteImage/small/' + cat.category_id + '.png">' +
                              '<a href="' + this.baseUrl + '/product?category_id=' + cat.category_id + '">' + cat.category_name + '</a>' +
                              '<div class="banner_div" name="' + cat.category_name + '"></div>' +
                              '</li>';
        }
        $('.banner_nav').html(navCategoryHtml);
    },
    
    // 渲染促销产品
    renderSpecialProducts: function(specialProductList) {
        if (!specialProductList || specialProductList.length === 0) return;
        
        var bannerHtml = '';
        for (var i = 0; i < specialProductList.length; i++) {
            var product = specialProductList[i];
            var displayStyle = i === 0 ? ' style="display: block;"' : '';
            bannerHtml += '<img src="' + this.baseUrl + '/res/images/fore/WebsiteImage/banner/' + 
                         product.product_id + '.jpg" name="' + product.product_id + 
                         '" id="banner' + (i + 1) + '"' + displayStyle + ' />';
        }
        $('.banner').html(bannerHtml);
    },
    
    // 渲染分类下的产品
    renderCategoryProducts: function(categoryList) {
        if (!categoryList || categoryList.length === 0) return;

        var goodsHtml = '';
        for (var i = 0; i < categoryList.length; i++) {
            var category = categoryList[i];
            if (category.productList && category.productList.length > 0) {
                goodsHtml += '<div class="banner_goods_type">' +
                            '<div class="banner_goods_title">' +
                            '<span></span>' +
                            '<p>' + category.category_name + '</p>' +
                            '</div>' +
                            '<a href="' + this.baseUrl + '/product?category_id=' + category.category_id + '">' +
                            '<img class="banner_goods_show" src="' + this.baseUrl + '/res/images/fore/WebsiteImage/show/' + category.category_id + '.jpg">' +
                            '</a>' +
                            '<div class="banner_goods_items">';

                for (var j = 0; j < category.productList.length && j < 8; j++) {
                    var product = category.productList[j];
                    var imageUrl = '';
                    if (product.singleProductImageList && product.singleProductImageList.length > 0) {
                        imageUrl = this.baseUrl + '/res/images/item/productSinglePicture/' + product.singleProductImageList[0].productImage_src;
                    }

                    goodsHtml += '<div class="banner_goods_item">' +
                                '<a href="' + this.baseUrl + '/product/' + product.product_id + '" class="goods_link"></a>' +
                                '<img src="' + imageUrl + '">' +
                                '<a href="' + this.baseUrl + '/product/' + product.product_id + '" class="goods_name">' + product.product_name + '</a>' +
                                '<span class="goods_price">￥' + product.product_sale_price + '</span>' +
                                '</div>';
                }

                goodsHtml += '</div></div>';
            }
        }
        $('.banner_goods').html(goodsHtml);

        // 绑定分类导航悬浮事件
        this.bindCategoryHoverEvents();

        // 初始化轮播图功能
        this.initBannerSlider();
    },

    // 绑定分类导航悬浮事件
    bindCategoryHoverEvents: function() {
        var self = this;

        // 悬浮到分类导航时
        $(".banner_nav>li").hover(function () {
            $(this).find(">a").css("color", "#6347ED");
            const div = $(this).find(">.banner_div").css("display", "block");
            if ($(this).attr("data-status") === "ajaxShow") {
                return;
            }
            $(this).attr("data-status", "ajaxShow");

            var categoryId = $(this).attr("data-toggle");
            self.loadCategoryProducts(categoryId, div);
        }, function () {
            $(this).find(">a").css("color", "#FFFFFF");
            $(this).find(".banner_div").css("display", "none");
        });
    },

    // 加载分类下的产品信息
    loadCategoryProducts: function(categoryId, targetDiv) {
        var self = this;

        $.ajax({
            type: "GET",
            url: self.baseUrl + "/product/nav/" + categoryId,
            dataType: "json",
            success: function (response) {
                if (response.code === 200 && response.data.success) {
                    var list = response.data.category.complexProductList;
                    for (var i = 0; i < list.length; i++) {
                        if (list[i].length === 0) {
                            continue;
                        }
                        targetDiv.append("<div class='hot_word'></div>");
                        var hot_word_div = targetDiv.children(".hot_word").last();
                        for (var j = 0; j < list[i].length; j++) {
                            var product_title = list[i][j].product_title;
                            var index = product_title.indexOf(' ');
                            if (index !== -1) {
                                product_title = product_title.substring(0, index);
                            }
                            hot_word_div.append("<a href='" + self.baseUrl + "/product/" + list[i][j].product_id + "'>" + product_title + "</a>");
                        }
                    }
                    // 热词样式
                    targetDiv.find("a").each(function () {
                        var random = parseInt(Math.random() * 10);
                        if (random > 7) {
                            $(this).css("color", "#6347ED");
                        }
                    });
                }
            },
            error: function (data) {
                console.error('加载分类产品失败');
            }
        });
    },
    
    // 用户退出
    logout: function() {
        var self = this;

        $.ajax({
            url: self.baseUrl + '/api/logout',
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                // 清除本地存储的Token
                self.removeToken();

                // 跳转到登录页
                window.location.href = self.baseUrl + '/login';
            },
            error: function() {
                // 即使请求失败，也清除本地Token
                self.removeToken();
                window.location.href = self.baseUrl + '/login';
            }
        });
    },
    
    // 初始化轮播图功能
    initBannerSlider: function() {
        var self = this;
        var index = 1;

        // 启动轮播图定时器
        function getTimer() {
            const banner = $(".banner");
            const sliders = $(".banner_slider>li");
            let color;
            const img = $("#banner" + index);
            $(".banner_main > a").attr("href", self.baseUrl + "/product/" + img.attr("name"));

            if (index === 1) {
                color = "#0F1322";
            } else if (index === 2 || index === 5) {
                color = "#E8E8E8";
            } else if (index === 3) {
                color = "#FBB4B0";
            } else if (index === 4) {
                color = "#262C42";
            } else {
                color = "#BD160D";
            }

            sliders.css("background", "rgba(0,0,0,0.4)");
            $("#slider_" + index).css("background", "rgba(255,255,255,0.4)");
            img.stop(true, true).fadeIn(1000);
            banner.stop(true, true).animate({
                "backgroundColor": color
            }, 800);
            img.siblings('img').stop(true, true).fadeOut(1000);
            index++;
            index = index > 6 ? 1 : index;
        }

        // 初始化轮播
        getTimer();
        let timer = setInterval(getTimer, 3000);
        $(".banner_main > a").attr("href", self.baseUrl + "/product/" + $("#banner1").attr("name"));

        // 悬浮到指定节点时
        $(".banner_slider>li").hover(function () {
            index = parseInt($(this).attr("id").substring($(this).attr("id").indexOf("_") + 1));
            clearInterval(timer);
            getTimer();
        }, function () {
            timer = setInterval(getTimer, 3000);
        });

        // 搜索框验证
        $('form').submit(function () {
            if ($(this).find("input[name='product_name']").val() === "") {
                alert("请输入关键字！");
                return false;
            }
        });
    },

    // 显示错误信息
    showError: function(message) {
        alert('错误：' + message);
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    HomeAPI.init();
});
