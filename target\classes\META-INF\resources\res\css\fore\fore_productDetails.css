nav {
    width: 100%;
    border-bottom: 1px solid #c8baaa;
}

.loginModel {
    display: none;
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    user-select: none;
    background-color: #000;
    opacity: .5;
    filter: alpha(opacity=50);
}

.loginDiv {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 350px;
    height: 436px;
    z-index: 99999;
    margin: auto;
    background-color: #FFFFFF;
    border: 10px solid rgba(0, 0, 0, 0.4);
}

.loginDiv > .loginDivHeader {
    background: #FF0036 url(../../images/fore/WebsiteImage/TB1VLoORXXXXXc4XXXXXXXXXXXX-132-41.png) 20px 10px no-repeat;
    height: 35px;
}

.loginDivHeader > .closeLoginDiv {
    position: absolute;
    z-index: 3;
    right: 10px;
    top: 10px;
    height: 16px;
    width: 16px;
    background: #ff0036 url(../../images/fore/WebsiteImage/TB1VLoORXXXXXc4XXXXXXXXXXXX-132-41.png) 0 -25px no-repeat;
}

.loginSwitch {
    position: absolute;
    width: 52px;
    height: 52px;
    right: 5px;
    top: 45px;
    background-image: url(../../images/fore/WebsiteImage/scan_icon.png);
    cursor: pointer;
    -webkit-background-size: cover;
    background-size: cover;
}

.loginSwitch_two {
    position: absolute;
    width: 52px;
    height: 52px;
    right: 5px;
    top: 45px;
    background-image: url(../../images/fore/WebsiteImage/pc_icon.png);
    cursor: pointer;
    -webkit-background-size: cover;
    background-size: cover;
}

.loginDiv > .loginMessage {
    position: absolute;
    top: 45px;
    right: 58px;
}

.loginMessage > .loginMessageMain {
    border: 1px solid #f3d995;
    height: 16px;
    line-height: 16px;
    background: #fefcee;
    color: #df9c1f;
    font-size: 12px;
    font-weight: 400;
    padding: 5px 20px 5px 15px;
    position: relative;
}

.loginMessageMain > img {
    margin-right: 8px;
    position: relative;
    bottom: 2px;
}

.loginMessageMain > .poptip-arrow {
    position: absolute;
    top: 8px;
    right: 0;
}

.poptip-arrow > em {
    position: absolute;
    width: 0;
    height: 0;
    top: 0;
    left: 1px;
    border: 6px solid rgba(255, 255, 255, 0);
    border-right-width: 0;
    border-left-color: #f3d995;
}

.poptip-arrow > span {
    position: absolute;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
    border: 6px solid rgba(255, 255, 255, 0);
    border-right-width: 0;
    border-left-color: #fefcee;
}

.loginDiv > .pwdLogin {
    padding: 25px 26px 20px;
}

.pwdLogin .loginInputDiv {
    position: relative;
    margin-top: 25px;

}

.loginInputDiv > .loginLabel {
    display: block;
    width: 38px;
    height: 38px;
    line-height: 38px;
    background: #ddd;
    text-align: center;
    position: absolute;
    top: 1px;
    left: 1px;
    font-size: 18px;
}

.loginLabel > i {
    color: #606060;
}

.loginInputDiv > .loginInput {
    color: #9b9b9b;
    width: 240px;
    font-size: 14px;
    line-height: 18px;
    height: 18px;
    padding: 11px 8px 11px 50px;
    border: 1px solid #CBCBCB;
}

.pwdLogin .loginButton {
    width: 300px;
    height: 42px;
    line-height: 42px;
    background-color: #ff0036;
    font-weight: 700;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    border-radius: 3px;
    border: 0;
    margin-top: 20px;
}

.loginButton:hover {
    background-color: #ff335e;
}

.pwdLogin .loginLinks {
    margin-top: 25px;
    text-align: right;
    font-size: 12px;
}

.loginLinks > a {
    margin-right: 10px;
    color: #6c6c6c;
    text-decoration: none;
}

.loginLinks > a:hover {
    color: #FF0036;
}

.pwdLogin .error_message {
    margin-top: 30px;
    text-align: center;
    font-size: 14px;
}

.error_message > p {
    position: relative;
    color: #c33;
    left: 20px;
    opacity: 0;
    margin: 0;
}

.loginTitle {
    display: block;
    margin-top: 15px;
    height: 18px;
    line-height: 18px;
    font-size: 16px;
    color: #3c3c3c;
    font-weight: 700;
}

.loginDiv > .qrcodeLogin {
    display: none;
    padding: 25px 26px 20px;
}

.qrcodeLogin > .qrcodeMain {
    position: relative;
    margin-top: 24px;
    height: 140px;
}

.qrcodeMain > #qrcodeA {
    position: absolute;
    left: 80px;
}

.qrcodeMain > #qrcodeB {
    position: absolute;
    opacity: 0;
    right: 12px;
    top: -15px;
}

.qrcodeLogin > .qrcodeFooter {
    width: 188px;
    margin: 24px auto 0;
    overflow: hidden;
}

.qrcodeFooter > img {
    float: left;
    margin-right: 10px;
    padding-top: 5px;
}

.qrcodeFooter > p {
    float: left;
    width: 144px;
    line-height: 18px;
    color: #6c6c6c;
    font-size: 12px;
}

.qrcodeFooter > p > a {
    color: #ff0036;
}

.qrcodeFooter > p > a:hover {
    text-decoration: none;
}

.qrcodeLogin > .loginLinks {
    margin-top: 15px;
    overflow: hidden;
    text-align: right;
    font-size: 12px;
}
.header {
    width: 1230px;
    margin: 0 auto;
    padding-top: 15px;
    height: 70px;
}

.header > .shopNameHeader {
    display: inline-block;
    height: 38px;
    line-height: 38px;
    font-size: 12px;
    font-weight: bold;
    font-family: '宋体', serif;
    border-left: 1px solid #f0f0f0;
    padding: 0 10px;
}

.header > .shopAssessHeader {
    border-left: 1px dashed #f0f0f0;
    border-right: 1px solid #f0f0f0;
}

.header > .shopSearchHeader {
    float: right;
    font-family: '宋体', serif;
    position: relative;
}

.shopSearchHeader .shopSearchInput {
    width: 455px;
    border: solid #ff0036;
    border-width: 3px 0 3px 3px;
    height: 30px;
    margin-right: 82px;
    position: relative;
}

.shopSearchInput > .searchInput {
    width: 367px;
    padding: 5px 3px 5px 5px;
    color: #000;
    margin: 0;
    height: 20px;
    line-height: 20px;
    outline: 0;
    border: none;
    font-size: 12px;
}

.shopSearchInput > .searchTmall {
    position: absolute;
    top: 0;
    right: 0;
    width: 80px;
    height: 30px;
    background-color: #FF0036;
    border: 0;
    color: #ffffff;
    font-size: 16px;
    font-family: "Microsoft YaHei UI", serif;
}

.shopSearchHeader .searchShop {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #333;
    color: #ffffff;
    font-size: 16px;
    font-family: "Microsoft YaHei UI", serif;
    width: 80px;
    height: 36px;
    border: 0;
}

.shopSearchHeader > ul {
    padding: 6px 0 0;
    height: 16px;
    margin-left: -13px;
    font-size: 13px;
    font-family: '宋体', serif;
    overflow: hidden;
}

.shopSearchHeader li {
    float: left;
    display: inline-block;
    padding: 0 12px;
    line-height: 1.1;
}

.shopSearchHeader li + li {
    border-left: 1px solid #cccccc;
}

.shopSearchHeader li > a {
    color: #999;
    text-decoration: none;
}

.shopSearchHeader li > a:hover {
    text-decoration: underline;
}

.shopImg {
    width: 1190px;
    margin: 0 auto;
}

.context {
    width: 1190px;
    height: 600px;
    margin: 20px auto;

}

.context > .context_left {
    float: left;
    height: 100%;
    position: relative;
}

.context_left > .context_img_ks {
    display: none;
    position: absolute;
    width: 418px;
    height: 418px;
    left: 460px;
    top: 20px;
    border: 1px solid #cccccc;
    overflow: hidden;
    text-align: center;
    z-index: 999;
    background-color: #FFFFFF;
}

.context_img_ks > img {
    position: absolute;
}

.context_left > .context_img {
    position: relative;
    overflow: hidden;
    margin: 20px;
}

.context_img > .context_img_main {
    width: 418px;
    height: 418px;
    border: 1px solid rgba(0, 0, 0, .05);
}

.context_img > .context_img_winSelector {
    position: absolute;
    cursor: move;
    top: 0;
    left: 0;
    width: 218px;
    height: 218px;
    display: none;
    background: url(../../images/fore/WebsiteImage/T12pdtXaldXXXXXXXX-2-2.png);
}

.context_left > .context_img_ul {
    margin: 0;
    text-align: center;
}

.context_img_ul > .context_img_li {
    margin: 0 6px;
    display: inline-block;
    width: 60px;
    height: 60px;
    cursor: pointer;
}

.context_img_li_hover {
    border: 2px solid #000000;
}

.context_img_li > img {
    width: 60px;
    height: 60px;
}

.context > .context_info {
    float: left;
    height: 100%;
}

.context_info > .context_info_name_div {
    padding: 20px 10px 12px;
    color: #000000;
}

.context_info_name_div > .context_info_name {
    width: 510px;
    padding-bottom: .3em;
    line-height: 1;
    font-size: 16px;
    font-weight: 700;
    font-family: "Microsoft YaHei UI", serif;
    margin: 0;
}

.context_info_name_div > .context_info_title {
    display: block;
    width: 510px;
    font-size: 14px;
    color: #FF0036;
    font-family: "Microsoft YaHei UI", serif;
}

.context_info > .context_info_main {
    background: #e9e9e9 url(../../images/fore/WebsiteImage/tmallItemContent.png) no-repeat;
    position: relative;
    padding-bottom: 5px;
    padding-top: 5px;
    margin-right: 20px;
}

.context_info_main > .context_info_main_ad {
    position: relative;
    height: 35px;
    margin: 0 10px 0 8px;
}

.context_info_main_ad > img {
    float: left;
    display: inline-block;
    margin: 10px 5px 0 0;
}

.context_info_main_ad > span {
    font: 400 12px/35px "Microsoft YaHei UI", tahoma, arial;
    color: #666;
}

.context_info_main_ad > a {
    float: right;
    font: normal 12px/35px "Microsoft YaHei UI", tahoma, arial;
    display: inline-block;
    color: #ff0036;
    cursor: pointer;
}

.context_info_main_ad > a > img {
    position: relative;
    bottom: 1px;
    padding-left: 5px;
}

.context_price_panel {
    margin: 0;
}

.context_price_panel > dt {
    font-family: "Microsoft YaHei UI", serif;
    font-weight: normal;
    color: #999;
    font-size: 12px;
    text-align: left;
    float: left;
    width: 69px;
    height: 20px;
    line-height: 20px;
    margin-left: 8px;
}

.context_price_panel > dd {
    margin-left: 70px;
    height: 20px;
    line-height: 20px;
}

.context_price_panel > dd > em {
    font-family: Arial, serif;
    font-style: normal;
}

.context_price_panel > dd > span {
    text-decoration: line-through;
    font-size: 14px;
    color: #333333;
    font-family: Arial, serif;
}

.context_promotePrice_panel {
    margin: 0;
}

.context_promotePrice_panel > dt {
    font-family: "Microsoft YaHei UI", serif;
    font-weight: normal;
    color: #999;
    font-size: 12px;
    text-align: left;
    float: left;
    width: 69px;
    margin-left: 8px;
    height: 42px;
    line-height: 42px;
}

.context_promotePrice_panel > dd {
    margin-left: 70px;
    height: 42px;
    line-height: 42px;
}

.context_promotePrice_panel > dd > em {
    font-style: normal;
    color: #FF0036;
    font-size: 18px;
    font-family: Arial, serif;
}

.context_promotePrice_panel > dd > span {
    font-style: normal;
    font-size: 30px;
    color: #FF0036;
    font-weight: bolder;
    font-family: Arial, serif;
}

.context_other_panel {
    border: dotted #c9c9c9;
    border-width: 1px 0;
    margin-top: 10px;
    margin-bottom: 0;
    margin-right: 20px;
    padding: 10px 0;
    position: relative;
}

.context_other_panel > li {
    font-family: "Microsoft YaHei UI", serif;
    color: #999;
    font-size: 12px;
    display: inline-block;
    width: 32%;
    text-align: center;
    line-height: 16px;
}

.context_other_panel > li + li {
    border-left: 1px solid #e5dfda;
}

.context_other_panel > li > span {
    font-family: tohoma, Arial, serif;
    display: inline-block;
    color: #FF0036;
    font-weight: 700;
    margin-left: 3px;
}

.context_other_panel > .tmall_points > span {
    color: #280;
}

.context_info_member {
    margin: 0;
    margin-top: 20px;
}

.context_info_member > dt {
    font-family: "Microsoft YaHei UI", serif;
    font-weight: normal;
    color: #999;
    font-size: 12px;
    text-align: left;
    float: left;
    width: 69px;
    margin-left: 8px;
    height: 31px;
    line-height: 31px;
}

.context_info_member > dd {
    height: 31px;
    line-height: 31px;
}

.context_info_member > dd > input {
    padding: 3px 2px 0 2px;
    font-size: 12px;
    font-family: Arial, serif;
    border: 1px solid #a7a6ac;
    width: 36px;
    height: 26px;
    color: #666;
    display: inline-block;
    float: left;
    margin-right: 5px;
    outline: none;
}

.amount-btn {
    float: left;
    display: inline-block;
    width: 18px;
}

.amount-btn > img {
    cursor: pointer;
    float: left;
}

.amount-btn > img + img {
    margin-top: 3px;
}

.amount_unit {
    font-family: "Microsoft YaHei UI", serif;
    font-weight: normal;
    color: #999;
    font-size: 12px;
    text-align: left;
    float: left;
    margin-left: 10px;
}

.context_info_member em {
    font-style: normal;
    font-family: "Microsoft YaHei UI", serif;
    margin-left: 20px;
    color: #999;
    font-size: 12px;
    text-align: left;
    float: left;
}

.context_buy {
    text-align: center;
    margin-top: 25px;
}

.context_buy input {
    display: inline-block;
    margin: 0 2px;
}

.context_buyNow {
    width: 178px;
    background-color: #ffeded;
    border: 1px solid #FF0036;
    color: #FF0036;
    font-family: "Microsoft YaHei UI", serif;
    height: 38px;
    line-height: 38px;
    font-size: 16px;
}

.context_addBuyCar {
    width: 178px;
    background-color: #FF0036;
    border: 1px solid #FF0036;
    color: #ffffff;
    font-family: "Microsoft YaHei UI", serif;
    height: 38px;
    line-height: 38px;
    font-size: 16px;
}

.context_notBuy {
    background-color: #f9f9f9;
    color: #818181;
    border: 1px solid #818181;
}

.context_notCar {
    background-color: #818181;
    color: #ffffff;
    border: 1px solid #818181;
}

.context_clear {
    width: 490px;
    margin: 0 20px;
    margin-top: 50px;
}

.context_clear > span {
    display: inline-block;
    font-family: "Microsoft YaHei UI", serif;
    color: #999;
    font-size: 12px;
    text-align: left;
    padding-right: 10px;
}

.context_clear > a {
    display: inline-block;
    font-family: "Microsoft YaHei UI", serif;
    color: #666;
    font-size: 12px;
    text-align: left;
    padding-right: 30px;
    padding-bottom: 3px;
}

.context_clear > a:hover {
    text-decoration: none;
}

.context_ul {
    width: 199px;
    float: right;
    border-left: 1px solid #f2f2f2;
    height: 100%;
    overflow: hidden;
}

.context_ul > .context_ul_head {
    position: relative;
    height: 15px;
    margin-top: 5px;
}

.context_ul_head > s {
    position: absolute;
    top: 10px;
    left: 30px;
    width: 140px;
    height: 0;
    border-top: 1px #c9c9c9 dotted;
    text-decoration: none;
}

.context_ul_head > span {
    position: absolute;
    color: #999999;
    text-align: center;
    width: 6em;
    top: 0;
    left: 50%;
    margin-left: -3em;
    background-color: #ffffff;
    font-family: "Microsoft YaHei UI", serif;
    font-size: 10px;
}

.context_ul > .context_ul_goodsList {
    height: 480px;
    overflow: hidden;
    position: relative;
}

.context_ul_goodsList > ul {
    padding: 10px 12px 2px 13px;
    position: relative;
}

.context_ul_goodsList > ul > .context_ul_main {
    height: 160px;
}

.context_ul_main > .context_ul_img {
    width: 140px;
    height: 140px;
    margin: 0 auto;
    position: relative;
    background-color: #f8f8f8;
    line-height: 120px;
    overflow: hidden;
}

.context_ul_img > a {
    display: block;
    text-align: center;
    color: #62574e;
    cursor: pointer;
    zoom: 1;
}

.context_ul_img > a > img {
    max-width: 140px;
    max-height: 140px;
}

.context_ul_img > p {
    width: 100%;
    left: 0;
    position: absolute;
    font-family: Arial, serif;
    line-height: 20px;
    background-color: rgba(255, 255, 255, .8);
    bottom: 0;
    text-align: center;
    margin: 0;
    font-size: 10px;
}

.context_ul > .context_ul_trigger {
    padding: 0 55px;
    height: 27px;
    margin: 0;
}

.context_ul_trigger > .ul_trigger_up {
    float: left;
    cursor: pointer;
    width: 27px;
    height: 27px;
    background: transparent url(../../images/fore/WebsiteImage/T1Z0VbFEdcXXbcIIcR-80-112.png) 0 0;
}

.context_ul_trigger > .ul_trigger_down {
    float: right;
    cursor: pointer;
    width: 27px;
    height: 27px;
    background: transparent url(../../images/fore/WebsiteImage/T1Z0VbFEdcXXbcIIcR-80-112.png) 0 -30px;
}

.ul_trigger_up > a, .ul_trigger_down > a {
    display: block;
    width: 100%;
    height: 100%;
}

.mainwrap {
    width: 790px;
    margin: 0 auto;
    overflow: hidden;
}

.mainwrap > .J_TabBarBox {
    width: 788px;
    border: 1px solid #dfdfdf;
    z-index: 9999;
}

.J_TabBarBox > ul {
    width: 100%;
    height: 48px;
    position: relative;
    background: #ffffff;
    margin: 0;
}

.J_TabBarBox li {
    float: left;
    line-height: 48px;
    display: block;
    cursor: pointer;
    margin-left: 1px;
    height: 48px;
}

.J_TabBarBox a {
    display: block;
    height: 100%;
    font-size: 10px;
    font-family: "Microsoft YaHei UI", serif;
    padding: 0 20px;
    color: #333333;
    border-right: 1px dotted #d2d2d2;
}

.J_TabBarBox a:hover {
    text-decoration: none;
}

.J_TabBarBox .tab-selected {
    position: relative;
    border-top: 2px solid #FF0036;
    height: 47px;
    margin: -1px 0 0 -1px;
    z-index: 1;
    padding: 0;
}

.J_TabBarBox .tab-selected:after {
    content: ' ';
    display: block;
    border: 5px solid transparent;
    border-top-color: #FF0036;
    width: 0;
    height: 0;
    font-family: Arial, serif;
    position: absolute;
    top: -1px;
    left: 50%;
    margin-left: -5px;
}

.J_TabBarBox .tab-selected > a {
    font-weight: 700;
    line-height: 46px;
    color: #FF0036;
    border-left: 1px solid #cfbfb1;
    border-right: 1px solid #cfbfb1;
}

.J_TabBarBox .J_GoodsReviews span {
    font-family: "Microsoft YaHei UI", serif;
    display: inline;
    margin: 0 5px;
    color: #38b;
}

.J_choose {
    width: 100%;
}

.J_img {
    width: 790px;
    position: relative;
    overflow: hidden;
    margin-bottom: 100px;
}

.msg {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 230px;
    height: 70px;
    line-height: 70px;
    color: white;
    border-radius: 5px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.75);
    font-size: 16px;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}