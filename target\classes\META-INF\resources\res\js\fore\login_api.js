/**
 * 登录页面API数据处理模块
 */
var LoginAPI = {
    
    // API基础路径
    baseUrl: '',
    
    // 验证码数据
    verifyCodeData: null,
    
    // 初始化
    init: function() {
        this.baseUrl = this.getContextPath();
        this.loadVerifyCode();
        this.bindEvents();
    },
    
    // 获取上下文路径
    getContextPath: function() {
        var pathName = document.location.pathname;
        var index = pathName.substr(1).indexOf("/");
        var result = pathName.substr(0, index + 1);
        return result === "/" ? "" : result;
    },
    
    // 绑定事件
    bindEvents: function() {
        var self = this;
        
        // 绑定登录表单提交事件
        $(".loginForm").off('submit').on('submit', function(e) {
            e.preventDefault();
            self.handleLogin();
            return false;
        });
        
        // 绑定验证码点击刷新事件
        $("#img_code").off('click').on('click', function() {
            self.loadVerifyCode();
        });
        
        // 绑定输入框焦点事件，隐藏错误信息
        $(".loginForm :text, .loginForm :password").off('focus').on('focus', function() {
            self.hideError();
        });
    },
    
    // 加载验证码
    loadVerifyCode: function() {
        var self = this;
        
        $.ajax({
            url: self.baseUrl + '/api/login/code',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.code === 200) {
                    self.verifyCodeData = response.data;
                    $("#img_code").attr("src", response.data.img);
                    // 将验证码存储到sessionStorage中用于验证
                    sessionStorage.setItem('verifyCode', response.data.code);
                } else {
                    console.error('获取验证码失败：', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('获取验证码请求失败：', error);
            }
        });
    },
    
    // 处理登录
    handleLogin: function() {
        var self = this;
        
        // 验证表单
        if (!this.validateForm()) {
            return;
        }
        
        var username = $.trim($("#name").val());
        var password = $.trim($("#password").val());
        var verifyCode = $.trim($("#code").val());
        
        // 显示登录中状态
        $(".loginButton").val("正在登录...");
        
        $.ajax({
            url: self.baseUrl + '/api/login',
            type: 'POST',
            data: {
                username: username,
                password: password,
                verifyCode: verifyCode
            },
            dataType: 'json',
            success: function(response) {
                $(".loginButton").val("登 录");
                
                if (response.code === 200) {
                    // 登录成功
                    self.handleLoginSuccess(response.data);
                } else {
                    // 登录失败
                    self.showError(response.message || "登录失败");
                    self.loadVerifyCode(); // 刷新验证码
                }
            },
            error: function(xhr, status, error) {
                $(".loginButton").val("登 录");
                self.showError("服务器异常，请刷新页面再试！");
                self.loadVerifyCode(); // 刷新验证码
            }
        });
    },
    
    // 处理登录成功
    handleLoginSuccess: function(data) {
        // 存储JWT Token
        localStorage.setItem('jwt_token', data.token);
        
        // 清除验证码
        sessionStorage.removeItem('verifyCode');
        
        // 跳转到首页
        window.location.href = this.baseUrl + '/';
    },
    
    // 表单验证
    validateForm: function() {
        var username = $.trim($("#name").val());
        var password = $.trim($("#password").val());
        var verifyCode = $.trim($("#code").val());
        var storedCode = sessionStorage.getItem('verifyCode');
        
        if (username === "") {
            this.showError("请输入用户名！");
            return false;
        }
        
        if (password === "") {
            this.showError("请输入密码！");
            return false;
        }
        
        if (verifyCode === "") {
            this.showError("请输入验证码！");
            return false;
        }
        
        if (storedCode && verifyCode.toLowerCase() !== storedCode.toLowerCase()) {
            this.showError("验证码错误！");
            this.loadVerifyCode(); // 刷新验证码
            return false;
        }
        
        return true;
    },
    
    // 显示错误信息
    showError: function(message) {
        $("#error_message_p").text(message);
        $(".error_message").show();
    },
    
    // 隐藏错误信息
    hideError: function() {
        $(".error_message").hide();
        $("#error_message_p").text("");
    },
    
    // 用户退出登录
    logout: function() {
        var self = this;
        
        $.ajax({
            url: self.baseUrl + '/api/logout',
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                // 清除本地存储的Token
                localStorage.removeItem('jwt_token');
                
                // 跳转到登录页
                window.location.href = self.baseUrl + '/login';
            },
            error: function() {
                // 即使请求失败，也清除本地Token
                localStorage.removeItem('jwt_token');
                window.location.href = self.baseUrl + '/login';
            }
        });
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    LoginAPI.init();
});

// 全局函数，供其他地方调用
function getHomeCode() {
    LoginAPI.loadVerifyCode();
}
