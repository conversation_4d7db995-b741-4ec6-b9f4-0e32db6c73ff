<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="335f8ffe-5004-4a83-9b6a-eb24d6df5c3a" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeHomeController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeHomeController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeLoginController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeLoginController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/META-INF/spring-devtools.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/META-INF/MANIFEST.MF" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/homePage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/homePage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/include/navigator.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/include/navigator.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/loginPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/loginPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_login.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_login.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\SDK\maven\current" />
        <option name="localRepository" value="D:\SDK\maven\localRepository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="showDialogWithAdvancedSettings" value="true" />
        <option name="useMavenConfig" value="false" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2y2V6zFn6Pfq1EsFxLNhAdZrCfh" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.funNetwork [clean].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.TmallApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "C:/Users/<USER>/Downloads/tmall-demo",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="TmallApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="funNetwork" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xq.tmall.TmallApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="335f8ffe-5004-4a83-9b6a-eb24d6df5c3a" name="更改" comment="" />
      <created>1749032484320</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749032484320</updated>
      <workItem from="1749032485716" duration="408000" />
      <workItem from="1749087522970" duration="2348000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>